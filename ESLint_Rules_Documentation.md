# ESLint 规则文档与 Code Review 常见错误

本文档记录了项目中使用的 ESLint 规则，以及在 Code Review 过程中常见的错误和最佳实践。

## 目录

1. [字符串和模板规则](#字符串和模板规则)
2. [通用 JavaScript 规则](#通用-javascript-规则)
3. [Import/Export 规则](#importexport-规则)
4. [React Hooks 规则](#react-hooks-规则)
5. [TypeScript 规则](#typescript-规则)
6. [React JSX 规则](#react-jsx-规则)
7. [代码风格和质量规则](#代码风格和质量规则)
8. [Code Review 常见错误总结](#code-review-常见错误总结)

---

## 字符串和模板规则

### `prefer-template: 'error'`

**说明**: 推荐使用模板字符串而不是字符串拼接

**❌ 错误示例**:

```javascript
const message = 'Hello ' + name + '!';
const url = baseUrl + '/api/' + endpoint;
```

**✅ 正确示例**:

```javascript
const message = `Hello ${name}!`;
const url = `${baseUrl}/api/${endpoint}`;
```

### `no-useless-concat: 'error'`

**说明**: 禁止不必要的字符串连接

**❌ 错误示例**:

```javascript
const str = 'Hello' + 'World'; // 应该直接写成 'HelloWorld'
const template = `Hello${'World'}`; // 应该直接写成 'HelloWorld'
```

**✅ 正确示例**:

```javascript
const str = 'HelloWorld';
const dynamic = `Hello${variable}World`;
```

---

## 通用 JavaScript 规则

### `prefer-const: 'warn'`

**说明**: 优先使用 const 声明不重新分配的变量

**❌ 错误示例**:

```javascript
let name = 'John'; // ❌ 变量不会重新分配，应该使用 const
let users = getUsers(); // ❌ 数组不会重新分配
```

**✅ 正确示例**:

```javascript
const name = 'John'; // ✅ 使用 const
const users = getUsers(); // ✅ 使用 const
let counter = 0; // ✅ 会重新分配的变量使用 let
counter++;
```

### `new-cap: ['error', { capIsNew: false, newIsCap: true, properties: true }]`

**说明**: 构造函数必须以大写字母开头

**❌ 错误示例**:

```javascript
const client = new api();
```

**✅ 正确示例**:

```javascript
const client = new Api();
```

### `no-console: ['warn', { allow: ['info', 'warn', 'error', 'debug'] }]`

**说明**: 限制 console 使用，只允许特定方法

**❌ 错误示例**:

```javascript
console.log('Debug info'); // ❌ 不允许 console.log
console.trace('Stack trace'); // ❌ 不允许 console.trace
```

**✅ 正确示例**:

```javascript
console.info('Information'); // ✅ 允许
console.warn('Warning'); // ✅ 允许
console.error('Error'); // ✅ 允许
console.debug('Debug'); // ✅ 允许
```

### `no-restricted-syntax: ['error']`

**说明**: 禁止使用特定的语法结构，包括：

- DebuggerStatement
- LabeledStatement
- WithStatement
- TSEnumDeclaration[const=true]
- TSExportAssignment

### `prefer-arrow-callback: ['error', { allowNamedFunctions: true, allowUnboundThis: true }]`

**说明**: 推荐使用箭头函数作为回调

**❌ 错误示例**:

```javascript
array.map(function (item) {
  return item * 2;
});
```

**✅ 正确示例**:

```javascript
array.map((item) => item * 2);
```

---

## Import/Export 规则

### `simple-import-sort/imports: 'error'`

**说明**: 自动排序 import 语句

**❌ 错误示例**:

```javascript
import { useState } from 'react';
import axios from 'axios';
import { Button } from './Button';
import type { User } from '../types';
import React from 'react';
```

**✅ 正确示例**:

```javascript
import React, { useState } from 'react';

import axios from 'axios';

import type { User } from '../types';
import { Button } from './Button';
```

### `simple-import-sort/exports: 'error'`

**说明**: 自动排序 export 语句

**❌ 错误示例**:

```javascript
export { Button } from './Button';
export type { User } from './types';
export { default as Header } from './Header';
export { Footer } from './Footer';
```

**✅ 正确示例**:

```javascript
export { default as Header } from './Header';
export { Button } from './Button';
export { Footer } from './Footer';
export type { User } from './types';
```

---

## React Hooks 规则

### `react-hooks/rules-of-hooks: 'error'`

**说明**: 确保 Hooks 只在函数组件和自定义 Hooks 的顶层调用

**❌ 错误示例**:

```javascript
function Component({ condition }) {
  if (condition) {
    const [state, setState] = useState(0); // ❌ 在条件语句中调用 Hook
  }

  for (let i = 0; i < 5; i++) {
    useEffect(() => {}); // ❌ 在循环中调用 Hook
  }
}
```

**✅ 正确示例**:

```javascript
function Component({ condition }) {
  const [state, setState] = useState(0);

  useEffect(() => {
    if (condition) {
      // 条件逻辑放在 Hook 内部
    }
  }, [condition]);
}
```

### `react-hooks/exhaustive-deps: 'warn'`

**说明**: 确保 useEffect 等 Hook 的依赖数组包含所有依赖项

**❌ 错误示例**:

```javascript
function Component({ userId }) {
  const [user, setUser] = useState(null);

  useEffect(() => {
    fetchUser(userId).then(setUser);
  }, []); // ❌ 缺少 userId 依赖
}
```

**✅ 正确示例**:

```javascript
function Component({ userId }) {
  const [user, setUser] = useState(null);

  useEffect(() => {
    fetchUser(userId).then(setUser);
  }, [userId]); // ✅ 包含所有依赖
}
```

### `react/jsx-no-useless-fragment: 'warn'`

**说明**: 避免不必要的 React Fragment

**❌ 错误示例**:

```jsx
<>
  <div>Single child</div>
</>
```

**✅ 正确示例**:

```jsx
<div>Single child</div>
```

### `react/no-array-index-key: 'warn'`

**说明**: 避免使用数组索引作为 key

**❌ 错误示例**:

```jsx
{
  items.map((item, index) => <div key={index}>{item}</div>);
}
```

**✅ 正确示例**:

```jsx
{
  items.map((item) => <div key={item.id}>{item}</div>);
}
```

### `react/no-danger: 'error'`

**说明**: 禁止使用 dangerouslySetInnerHTML

**❌ 错误示例**:

```jsx
<div dangerouslySetInnerHTML={{ __html: content }} />
```

**✅ 正确示例**:

```jsx
<div>{sanitizedContent}</div>
```

### `react/no-deprecated: 'error'`

**说明**: 禁止使用已废弃的 React API

### `react/no-direct-mutation-state: 'error'`

**说明**: 禁止直接修改 state

**❌ 错误示例**:

```jsx
this.state.count = 3;
```

**✅ 正确示例**:

```jsx
this.setState({ count: 3 });
```

### `react/no-find-dom-node: 'error'`

**说明**: 禁止使用 findDOMNode

### `react/no-is-mounted: 'error'`

**说明**: 禁止使用 isMounted

### `react/no-render-return-value: 'error'`

**说明**: 禁止使用 render 方法的返回值

### `react/no-string-refs: 'error'`

**说明**: 禁止使用字符串 refs

### `react/no-unknown-property: 'error'`

**说明**: 禁止使用未知的 DOM 属性

### `react/require-render-return: 'error'`

**说明**: 要求 render 方法有返回值

### `react/void-dom-elements-no-children: 'error'`

**说明**: 禁止给 void 元素添加子元素

---

## TypeScript 规则

### `@typescript-eslint/no-unused-vars: ['error', { argsIgnorePattern: '^_' }]`

**说明**: 禁止未使用的变量，但允许以下划线开头的参数

**❌ 错误示例**:

```typescript
function processData(data: Data, unused: string) {
  // ❌ unused 未使用
  return data.value;
}
```

**✅ 正确示例**:

```typescript
function processData(data: Data, _unused: string) {
  // ✅ 下划线前缀表示故意未使用
  return data.value;
}

// 或者直接删除未使用的参数
function processData(data: Data) {
  return data.value;
}
```

### `@typescript-eslint/consistent-type-imports: ['error', { prefer: 'type-imports' }]`

**说明**: 强制使用 type-only 导入

**❌ 错误示例**:

```typescript
import { User, ApiResponse } from './types'; // ❌ 类型应该使用 type 导入
import { api } from './api';
```

**✅ 正确示例**:

```typescript
import type { User, ApiResponse } from './types'; // ✅ 明确标识类型导入
import { api } from './api';
```

### `@typescript-eslint/no-explicit-any: 'warn'`

**说明**: 避免使用 any 类型

**❌ 错误示例**:

```typescript
function processData(data: any): any {
  // ❌ 使用 any 失去类型安全
  return data.someProperty;
}
```

**✅ 正确示例**:

```typescript
interface DataType {
  someProperty: string;
}

function processData(data: DataType): string {
  return data.someProperty;
}

// 或者使用泛型
function processData<T>(data: T): T {
  return data;
}
```

### `@typescript-eslint/prefer-optional-chain: 'warn'`

**说明**: 推荐使用可选链操作符

**❌ 错误示例**:

```typescript
const value = obj && obj.prop && obj.prop.value;
```

**✅ 正确示例**:

```typescript
const value = obj?.prop?.value;
```

### `@typescript-eslint/ban-ts-comment: ['warn', { 'ts-expect-error': 'allow-with-description' }]`

**说明**: 限制 TypeScript 注释的使用

**❌ 错误示例**:

```typescript
// @ts-ignore
function test() {}
```

**✅ 正确示例**:

```typescript
// @ts-expect-error: This is a known issue with the types
function test() {}
```

---

## React JSX 规则

### `react/jsx-sort-props: 'error'`

**说明**: JSX 属性按特定顺序排列

**❌ 错误示例**:

```jsx
<Button onClick={handleClick} disabled className="btn-primary" key="button1" type="submit">
  Submit
</Button>
```

**✅ 正确示例**:

```jsx
<Button
  key="button1" // reserved props 优先
  className="btn-primary"
  disabled // boolean props
  type="submit"
  onClick={handleClick} // callbacks 最后
>
  Submit
</Button>
```

### `react/jsx-no-constructed-context-values: 'error'`

**说明**: 避免在 JSX 中创建新的对象作为 Context 值

**❌ 错误示例**:

```jsx
function App() {
  return (
    <Context.Provider value={{ user: 'John', age: 25 }}>
      {' '}
      {/* ❌ 每次渲染都创建新对象 */}
      <Child />
    </Context.Provider>
  );
}
```

**✅ 正确示例**:

```jsx
function App() {
  const contextValue = useMemo(() => ({ user: 'John', age: 25 }), []);

  return (
    <Context.Provider value={contextValue}>
      <Child />
    </Context.Provider>
  );
}
```

### `react/self-closing-comp: 'error'`

**说明**: 没有子元素的组件应该自闭合

**❌ 错误示例**:

```jsx
<div className="container"></div>
<Input type="text"></Input>
```

**✅ 正确示例**:

```jsx
<div className="container" />
<Input type="text" />
```

### `react/jsx-no-duplicate-props: 'error'`

**说明**: 禁止重复的属性

**❌ 错误示例**:

```jsx
<div className="foo" className="bar" />
```

**✅ 正确示例**:

```jsx
<div className="foo bar" />
```

### `react/jsx-pascal-case: 'error'`

**说明**: 组件名称必须使用 PascalCase

**❌ 错误示例**:

```jsx
<userProfile />
```

**✅ 正确示例**:

```jsx
<UserProfile />
```

### `react/no-unused-prop-types: 'warn'`

**说明**: 检测未使用的 prop 类型

---

## 代码风格和质量规则

### `no-var: 'error'`

**说明**: 禁止使用 var 声明变量

**❌ 错误示例**:

```javascript
var name = 'John'; // ❌ 使用 var
var count = 0;
```

**✅ 正确示例**:

```javascript
const name = 'John'; // ✅ 使用 const
let count = 0; // ✅ 使用 let
```

### `no-multiple-empty-lines: ['error', { max: 1, maxEOF: 0 }]`

**说明**: 限制连续空行数量

**❌ 错误示例**:

```javascript
function foo() {
  return 'bar';
}

function baz() {
  // ❌ 超过一个空行
  return 'qux';
}
```

**✅ 正确示例**:

```javascript
function foo() {
  return 'bar';
}

function baz() {
  // ✅ 最多一个空行
  return 'qux';
}
```

### `no-trailing-spaces: 'error'`

**说明**: 禁止行尾空格

### `eol-last: 'error'`

**说明**: 文件末尾需要空行

### `comma-dangle: ['error', 'always-multiline']`

**说明**: 多行时要求尾随逗号

**❌ 错误示例**:

```javascript
const obj = {
  name: 'John',
  age: 25, // ❌ 缺少尾随逗号
};

const arr = [
  'item1',
  'item2', // ❌ 缺少尾随逗号
];
```

**✅ 正确示例**:

```javascript
const obj = {
  name: 'John',
  age: 25, // ✅ 多行时需要尾随逗号
};

const arr = [
  'item1',
  'item2', // ✅ 多行时需要尾随逗号
];

const single = { name: 'John' }; // ✅ 单行不需要尾随逗号
```

### `semi: ['error', 'always']`

**说明**: 要求语句末尾使用分号

**❌ 错误示例**:

```javascript
const name = 'John'; // ❌ 缺少分号
const age = 25; // ❌ 缺少分号
```

**✅ 正确示例**:

```javascript
const name = 'John'; // ✅ 使用分号
const age = 25; // ✅ 使用分号
```

### `object-curly-spacing: ['error', 'always']`

**说明**: 对象字面量的大括号内要求空格

**❌ 错误示例**:

```javascript
const obj = { name: 'John', age: 25 }; // ❌ 缺少空格
```

**✅ 正确示例**:

```javascript
const obj = { name: 'John', age: 25 }; // ✅ 有空格
```

### `no-multiple-empty-lines: ['error', { max: 1, maxEOF: 0 }]`

**说明**: 限制连续空行数量

**❌ 错误示例**:

```javascript
function foo() {
  return 'bar';
}

function baz() {
  // ❌ 超过一个空行
  return 'qux';
}
```

**✅ 正确示例**:

```javascript
function foo() {
  return 'bar';
}

function baz() {
  // ✅ 最多一个空行
  return 'qux';
}
```

### `new-cap: ['error', { capIsNew: false, newIsCap: true, properties: true }]`

**说明**: 构造函数必须以大写字母开头

**❌ 错误示例**:

```javascript
const client = new api();
```

**✅ 正确示例**:

```javascript
const client = new Api();
```

### `prefer-arrow-callback: ['error', { allowNamedFunctions: true, allowUnboundThis: true }]`

**说明**: 推荐使用箭头函数作为回调

**❌ 错误示例**:

```javascript
array.map(function (item) {
  return item * 2;
});
```

**✅ 正确示例**:

```javascript
array.map((item) => item * 2);
```

### `no-restricted-syntax: ['error']`

**说明**: 禁止使用特定的语法结构，包括：

- DebuggerStatement
- LabeledStatement
- WithStatement
- TSEnumDeclaration[const=true]
- TSExportAssignment

### `no-trailing-spaces: 'error'`

**说明**: 禁止行尾空格

### `eol-last: 'error'`

**说明**: 文件末尾需要空行

### `array-bracket-spacing: ['error', 'never']`

**说明**: 数组括号内不允许空格

**❌ 错误示例**:

```javascript
const arr = [1, 2, 3]; // ❌ 有空格
```

**✅ 正确示例**:

```javascript
const arr = [1, 2, 3]; // ✅ 无空格
```

### `no-unneeded-ternary: 'error'`

**说明**: 禁止不必要的三元表达式

**❌ 错误示例**:

```javascript
const isYes = answer === 1 ? true : false;
```

**✅ 正确示例**:

```javascript
const isYes = answer === 1;
```

### `no-param-reassign: 'error'`

**说明**: 禁止修改函数参数

**❌ 错误示例**:

```javascript
function updateUser(user) {
  user.name = 'Updated'; // ❌ 直接修改参数
  return user;
}
```

**✅ 正确示例**:

```javascript
function updateUser(user) {
  return { ...user, name: 'Updated' }; // ✅ 返回新对象
}
```

### `no-return-assign: 'error'`

**说明**: 禁止在返回语句中赋值

### `no-sequences: 'error'`

**说明**: 禁止使用逗号操作符

### `no-unused-expressions: 'warn'`

**说明**: 禁止未使用的表达式

### `no-void: 'error'`

**说明**: 禁止使用 void 操作符

### `no-useless-constructor: 'error'`

**说明**: 禁止不必要的构造函数

### `no-useless-rename: 'error'`

**说明**: 禁止不必要的重命名

### `operator-assignment: ['error', 'always']`

**说明**: 要求使用操作符简写

**❌ 错误示例**:

```javascript
x = x + y;
```

**✅ 正确示例**:

```javascript
x += y;
```

### `prefer-spread: 'error'`

**说明**: 推荐使用扩展运算符

### `prefer-rest-params: 'error'`

**说明**: 推荐使用剩余参数

### `prefer-object-spread: 'error'`

**说明**: 推荐使用对象扩展运算符

### `no-useless-escape: 'error'`

**说明**: 禁止不必要的转义

### `no-useless-return: 'error'`

**说明**: 禁止不必要的 return

### `no-useless-catch: 'error'`

**说明**: 禁止不必要的 catch 子句

### `no-useless-computed-key: 'error'`

**说明**: 禁止不必要的计算属性

### `no-useless-call: 'error'`

**说明**: 禁止不必要的 .call() 和 .apply()

### `no-constant-condition: 'error'`

**说明**: 禁止在条件语句中使用常量

### `no-duplicate-case: 'error'`

**说明**: 禁止 switch 语句中的重复 case

### `no-empty: ['error', { allowEmptyCatch: true }]`

**说明**: 禁止空块语句，但允许空的 catch 子句

### `no-extra-boolean-cast: 'error'`

**说明**: 禁止不必要的布尔类型转换

### `no-extra-semi: 'error'`

**说明**: 禁止多余的分号

### `no-async-promise-executor: 'error'`

**说明**: 禁止在 Promise 构造函数中使用 async

**❌ 错误示例**:

```javascript
new Promise(async (resolve, reject) => {
  await doSomething();
  resolve();
});
```

**✅ 正确示例**:

```javascript
new Promise((resolve, reject) => {
  doSomething().then(resolve);
});
```

---

## Code Review 常见错误总结

### 1. 字符串拼接错误

```javascript
// ❌ 常见错误
const message = 'Welcome ' + user.name + ', you have ' + notifications.length + ' notifications';

// ✅ 应该改为
const message = `Welcome ${user.name}, you have ${notifications.length} notifications`;
```

### 2. React Hooks 依赖缺失

```javascript
// ❌ 常见错误
useEffect(() => {
  if (userId) {
    fetchUserData(userId);
  }
}, []); // 缺少 userId 依赖

// ✅ 应该改为
useEffect(() => {
  if (userId) {
    fetchUserData(userId);
  }
}, [userId]);
```

### 3. TypeScript any 类型滥用

```javascript
// ❌ 常见错误
const handleResponse = (response: any) => {
  return response.data.items;
};

// ✅ 应该改为
interface ApiResponse<T> {
  data: {
    items: T[];
  };
}

const handleResponse = <T>(response: ApiResponse<T>) => {
  return response.data.items;
};
```

### 4. 对象/数组变异

```javascript
// ❌ 常见错误
function addItem(items, newItem) {
  items.push(newItem); // 直接修改参数
  return items;
}

// ✅ 应该改为
function addItem(items, newItem) {
  return [...items, newItem]; // 返回新数组
}
```

### 5. 不必要的 console.log

```javascript
// ❌ 常见错误
function processData(data) {
  console.log('Processing data:', data); // 应该移除或改为适当的日志方法
  return data.map((item) => item.value);
}

// ✅ 应该改为
function processData(data) {
  console.debug('Processing data:', data); // 或者完全移除
  return data.map((item) => item.value);
}
```

### 6. 未使用的变量和导入

```javascript
// ❌ 常见错误
import React, { useState, useEffect } from 'react'; // useEffect 未使用
import { api, utils } from './helpers'; // utils 未使用

function Component() {
  const [count, setCount] = useState(0);
  const unusedVar = 'not used'; // 未使用的变量

  return <div>{count}</div>;
}
```

### 7. 不正确的可选链使用

```typescript
// ❌ 常见错误
const value = obj && obj.prop && obj.prop.nested && obj.prop.nested.value;

// ✅ 应该改为
const value = obj?.prop?.nested?.value;
```

### 8. Context 值重复创建

```jsx
// ❌ 常见错误
function App() {
  const [user, setUser] = useState(null);

  return (
    <UserContext.Provider value={{ user, setUser }}>
      <Child />
    </UserContext.Provider>
  );
}

// ✅ 应该改为
function App() {
  const [user, setUser] = useState(null);
  const contextValue = useMemo(() => ({ user, setUser }), [user]);

  return (
    <UserContext.Provider value={contextValue}>
      <Child />
    </UserContext.Provider>
  );
}
```

### 9. 数组索引作为 key

```jsx
// ❌ 常见错误
{
  items.map((item, index) => <ListItem key={index} data={item} />);
}

// ✅ 应该改为
{
  items.map((item) => <ListItem key={item.id} data={item} />);
}
```

### 10. 不必要的 Fragment

```jsx
// ❌ 常见错误
return (
  <>
    <div>Single element</div>
  </>
);

// ✅ 应该改为
return <div>Single element</div>;
```

### 11. 缺少错误边界处理

```jsx
// ❌ 常见错误
function Component() {
  const data = fetchData(); // 可能抛出错误
  return <div>{data.value}</div>;
}

// ✅ 应该改为
function Component() {
  try {
    const data = fetchData();
    return <div>{data?.value}</div>;
  } catch (error) {
    console.error('Error fetching data:', error);
    return <div>Error loading data</div>;
  }
}
```

## 性能相关的常见错误

### 12. 在渲染中创建函数

```jsx
// ❌ 常见错误
function Component({ items }) {
  return (
    <div>
      {items.map((item) => (
        <button key={item.id} onClick={() => handleClick(item.id)}>
          {item.name}
        </button>
      ))}
    </div>
  );
}

// ✅ 应该改为
function Component({ items }) {
  const handleItemClick = useCallback((id) => {
    handleClick(id);
  }, []);

  return (
    <div>
      {items.map((item) => (
        <button key={item.id} onClick={() => handleItemClick(item.id)}>
          {item.name}
        </button>
      ))}
    </div>
  );
}
```

### 13. 缺少 memo 优化

```jsx
// ❌ 常见错误
function ExpensiveComponent({ data, onUpdate }) {
  const processedData = expensiveCalculation(data);

  return <div>{processedData}</div>;
}

// ✅ 应该改为
const ExpensiveComponent = memo(function ExpensiveComponent({ data, onUpdate }) {
  const processedData = useMemo(() => expensiveCalculation(data), [data]);

  return <div>{processedData}</div>;
});
```

### 14. 不必要的重新渲染

```jsx
// ❌ 常见错误
function Parent() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <button onClick={() => setCount((c) => c + 1)}>Count: {count}</button>
      <ExpensiveChild data={{ value: 'static' }} />
    </div>
  );
}

// ✅ 应该改为
function Parent() {
  const [count, setCount] = useState(0);
  const staticData = useMemo(() => ({ value: 'static' }), []);

  return (
    <div>
      <button onClick={() => setCount((c) => c + 1)}>Count: {count}</button>
      <ExpensiveChild data={staticData} />
    </div>
  );
}
```
