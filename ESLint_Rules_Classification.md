# ESLint 规则分类说明

本文档说明了 `.eslintrc.js` 文件中 ESLint 规则的分类和组织结构。

## 📋 规则分类概览

### 1. 字符串和模板字面量规则 (2 个)
- `prefer-template` - 推荐使用模板字符串而不是字符串拼接
- `no-useless-concat` - 禁止不必要的字符串字面量或模板字面量的连接

### 2. 变量声明和赋值规则 (3 个)
- `prefer-const` - 优先使用 const 声明不重新分配的变量
- `no-var` - 禁止使用 var 声明变量
- `no-param-reassign` - 禁止修改函数参数

### 3. 函数和回调规则 (2 个)
- `prefer-arrow-callback` - 推荐使用箭头函数作为回调
- `new-cap` - 构造函数必须以大写字母开头

### 4. 控制台和调试规则 (2 个)
- `no-console` - 限制 console 使用，只允许特定方法
- `no-restricted-syntax` - 禁止使用特定的语法结构

### 5. Import/Export 规则 (2 个)
- `simple-import-sort/imports` - 自动排序 import 语句
- `simple-import-sort/exports` - 自动排序 export 语句

### 6. TypeScript 相关规则 (5 个)
- `@typescript-eslint/no-unused-vars` - 禁止未使用的变量
- `@typescript-eslint/consistent-type-imports` - 强制使用 type-only 导入
- `@typescript-eslint/no-explicit-any` - 避免使用 any 类型
- `@typescript-eslint/prefer-optional-chain` - 推荐使用可选链操作符
- `@typescript-eslint/ban-ts-comment` - 限制 TypeScript 注释的使用

### 7. React Hooks 规则 (2 个)
- `react-hooks/rules-of-hooks` - 确保 Hooks 只在函数组件和自定义 Hooks 的顶层调用
- `react-hooks/exhaustive-deps` - 确保 useEffect 等 Hook 的依赖数组包含所有依赖项

### 8. React 组件和 JSX 规则 (15 个)
#### JSX 语法规则 (6 个)
- `react/jsx-no-duplicate-props` - 禁止重复的属性
- `react/jsx-pascal-case` - 组件名称必须使用 PascalCase
- `react/jsx-sort-props` - JSX 属性按特定顺序排列
- `react/jsx-no-constructed-context-values` - 避免在 JSX 中创建新的对象作为 Context 值
- `react/jsx-no-useless-fragment` - 避免不必要的 React Fragment
- `react/self-closing-comp` - 没有子元素的组件应该自闭合

#### React 组件规则 (9 个)
- `react/no-array-index-key` - 避免使用数组索引作为 key
- `react/no-danger` - 禁止使用 dangerouslySetInnerHTML
- `react/no-deprecated` - 禁止使用已废弃的 React API
- `react/no-direct-mutation-state` - 禁止直接修改 state
- `react/no-find-dom-node` - 禁止使用 findDOMNode
- `react/no-is-mounted` - 禁止使用 isMounted
- `react/no-render-return-value` - 禁止使用 render 方法的返回值
- `react/no-string-refs` - 禁止使用字符串 refs
- `react/no-unknown-property` - 禁止使用未知的 DOM 属性
- `react/no-unused-prop-types` - 检测未使用的 prop 类型
- `react/require-render-return` - 要求 render 方法有返回值
- `react/void-dom-elements-no-children` - 禁止给 void 元素添加子元素

### 9. 代码格式化和风格规则 (7 个)
#### 空格和换行 (5 个)
- `no-multiple-empty-lines` - 限制连续空行数量
- `no-trailing-spaces` - 禁止行尾空格
- `eol-last` - 文件末尾需要空行
- `object-curly-spacing` - 对象字面量的大括号内要求空格
- `array-bracket-spacing` - 数组括号内不允许空格

#### 标点符号 (2 个)
- `comma-dangle` - 多行时要求尾随逗号
- `semi` - 要求语句末尾使用分号

### 10. 代码质量和最佳实践规则 (25 个)
#### 条件和逻辑 (3 个)
- `no-unneeded-ternary` - 禁止不必要的三元表达式
- `no-constant-condition` - 禁止在条件语句中使用常量
- `no-duplicate-case` - 禁止 switch 语句中的重复 case

#### 语句和表达式 (4 个)
- `no-return-assign` - 禁止在返回语句中赋值
- `no-sequences` - 禁止使用逗号操作符
- `no-unused-expressions` - 禁止未使用的表达式
- `no-void` - 禁止使用 void 操作符

#### 函数和对象 (3 个)
- `no-useless-constructor` - 禁止不必要的构造函数
- `no-useless-rename` - 禁止不必要的重命名
- `operator-assignment` - 要求使用操作符简写

#### ES6+ 特性 (3 个)
- `prefer-spread` - 推荐使用扩展运算符
- `prefer-rest-params` - 推荐使用剩余参数
- `prefer-object-spread` - 推荐使用对象扩展运算符

#### 不必要的代码 (5 个)
- `no-useless-escape` - 禁止不必要的转义
- `no-useless-return` - 禁止不必要的 return
- `no-useless-catch` - 禁止不必要的 catch 子句
- `no-useless-computed-key` - 禁止不必要的计算属性
- `no-useless-call` - 禁止不必要的 .call() 和 .apply()

#### 错误处理 (3 个)
- `no-empty` - 禁止空块语句，但允许空的 catch 子句
- `no-extra-boolean-cast` - 禁止不必要的布尔类型转换
- `no-extra-semi` - 禁止多余的分号

#### 异步代码 (1 个)
- `no-async-promise-executor` - 禁止在 Promise 构造函数中使用 async

### 11. 注释掉的规则（可选启用）(2 个)
- `no-await-in-loop` - 禁止在循环中使用 await
- `require-await` - 要求 async 函数中有 await

## 📊 统计信息

- **总规则数**: 65 个
- **错误级别 (error)**: 55 个
- **警告级别 (warn)**: 10 个
- **注释掉的规则**: 2 个

## 🎯 分类原则

1. **功能相关性**: 将功能相似的规则归为一类
2. **技术栈分离**: React、TypeScript 等技术栈相关规则单独分类
3. **影响范围**: 按照规则影响的代码范围进行分组
4. **使用频率**: 常用规则放在前面，便于查找和维护

## 📝 维护建议

1. **新增规则**: 按照分类原则添加到对应分类中
2. **规则调整**: 修改规则级别时保持分类不变
3. **注释维护**: 每个规则都有清晰的中文注释说明
4. **定期审查**: 定期检查规则的有效性和必要性
